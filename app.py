import streamlit as st
import pandas as pd
import title_generator  # ✅ Calls your backend

st.title("🛒 AI Product Title Generator")

input_ids = st.text_input("Enter up to 10 Display IDs (comma separated):")

if st.button("Generate Titles"):
    ids = [i.strip() for i in input_ids.split(',') if i.strip()][:10]
    if not ids:
        st.warning("Please enter at least one valid display ID.")
    else:
        results = []
        with st.spinner("Generating titles..."):
            for display_id in ids:
                output_row = title_generator.process_display_id(display_id)
                if output_row:
                    results.append(output_row)
        df = pd.DataFrame(results)
        st.dataframe(df)
        import io
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
        output.seek(0)

        st.download_button(
            "Download Results as Excel",
            data=output,
            file_name="generated_titles.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
)

