import streamlit as st
import pandas as pd
import title_generator  # ✅ Calls your backend
import io

# Page configuration
st.set_page_config(
    page_title="AI Product Title Generator",
    page_icon="🛒",
    layout="wide"
)

# Initialize session state variables
if 'results_data' not in st.session_state:
    st.session_state.results_data = []
if 'regenerated_results' not in st.session_state:
    st.session_state.regenerated_results = []
if 'regenerate_count' not in st.session_state:
    st.session_state.regenerate_count = 0
if 'original_ids' not in st.session_state:
    st.session_state.original_ids = []
if 'feedback_data' not in st.session_state:
    st.session_state.feedback_data = {}

st.title("🛒 AI Product Title Generator")
st.markdown("Generate SEO-optimized product titles using AI. Enter up to 10 Display IDs to get started.")

# Input section
st.subheader("📝 Input Display IDs")
input_ids = st.text_area(
    "Enter Display IDs (comma or newline separated):",
    placeholder="23410447562, 20602194862, 21952292462\nor\n23410447562\n20602194862\n21952292462",
    height=100
)

# Process input
if st.button("🚀 Generate Titles", type="primary"):
    # Parse input - handle both comma and newline separation
    ids = []
    if input_ids:
        # Split by both comma and newline, then clean up
        raw_ids = input_ids.replace(',', '\n').split('\n')
        ids = [i.strip() for i in raw_ids if i.strip()][:10]

    if not ids:
        st.warning("⚠️ Please enter at least one valid display ID.")
    else:
        # Reset session state for new generation
        st.session_state.results_data = []
        st.session_state.regenerated_results = []
        st.session_state.regenerate_count = 0
        st.session_state.original_ids = ids.copy()
        st.session_state.feedback_data = {}

        st.info(f"Processing {len(ids)} display ID(s)...")

        results = []
        failed_ids = []

        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()

        with st.spinner("Generating titles..."):
            for i, display_id in enumerate(ids):
                status_text.text(f"Processing ID: {display_id}")
                progress_bar.progress((i + 1) / len(ids))

                output_row = title_generator.process_display_id(display_id)
                if output_row and "error" not in output_row:
                    # Set default feedback to empty for new results
                    output_row['feedback'] = ""
                    results.append(output_row)
                else:
                    failed_ids.append(display_id)

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        # Store results in session state
        st.session_state.results_data = results

        # Show failed IDs if any
        if failed_ids:
            st.warning(f"⚠️ Failed to process {len(failed_ids)} ID(s): {', '.join(failed_ids)}")

        if not results:
            st.error("❌ No titles were generated. Please check your Display IDs and try again.")

# Display results if available in session state
if st.session_state.results_data:
    st.success(f"✅ Successfully generated {len(st.session_state.results_data)} titles!")

    # Create DataFrame from session state
    df = pd.DataFrame(st.session_state.results_data)

    # Display results with feedback selection
    st.subheader("📊 Original Generated Results")
    st.markdown("**Instructions for Auditor:** Please review each generated title and select 'Yes' if approved or 'No' if regeneration is needed.")

    # Create feedback selection interface using columns
    for idx, row in df.iterrows():
        display_id = row['display_id']

        with st.container():
            col1, col2, col3, col4 = st.columns([2, 4, 1, 1])

            with col1:
                st.text(f"ID: {display_id}")

            with col2:
                st.text(f"Title: {row['generated_title']}")

            with col3:
                # Use unique key for each feedback selection
                current_feedback = st.session_state.feedback_data.get(display_id, "")
                feedback = st.selectbox(
                    "Feedback",
                    options=["", "Yes", "No"],
                    index=["", "Yes", "No"].index(current_feedback) if current_feedback in ["", "Yes", "No"] else 0,
                    key=f"feedback_{display_id}",
                    label_visibility="collapsed"
                )
                # Update feedback in session state
                st.session_state.feedback_data[display_id] = feedback

            with col4:
                st.text(f"Model: {row['llm_model_used']}")

    # Display detailed table (read-only)
    st.subheader("📋 Detailed View")

    # Add feedback column to display dataframe
    display_df = df.copy()
    display_df['feedback'] = display_df['display_id'].map(st.session_state.feedback_data).fillna("")

    st.dataframe(
        display_df,
        use_container_width=True,
        column_config={
            "url": st.column_config.LinkColumn("Product URL"),
            "generated_title": st.column_config.TextColumn("Generated Title", width="large"),
            "specs": st.column_config.TextColumn("Specifications", width="medium"),
            "material": st.column_config.TextColumn("Material", width="small"),
            "tags": st.column_config.TextColumn("Tags", width="medium"),
            "llm_model_used": st.column_config.TextColumn("AI Model", width="small"),
            "feedback": st.column_config.TextColumn("Feedback", width="small"),
        },
        hide_index=True
    )

    # Regenerate functionality
    st.subheader("🔄 Regenerate Titles")

    # Count feedback responses from session state
    feedback_values = list(st.session_state.feedback_data.values())
    no_feedback_count = feedback_values.count('No')
    yes_feedback_count = feedback_values.count('Yes')

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Approved (Yes)", yes_feedback_count)
    with col2:
        st.metric("Need Regeneration (No)", no_feedback_count)
    with col3:
        st.metric("Regenerations Used", f"{st.session_state.regenerate_count}/3")

    # Regenerate button
    regenerate_disabled = (st.session_state.regenerate_count >= 3) or (no_feedback_count == 0)

    if st.button(
        f"🔄 Regenerate Titles for 'No' Cases ({no_feedback_count} items)",
        type="secondary",
        disabled=regenerate_disabled,
        help="Regenerate titles only for items marked as 'No'. Limited to 3 regenerations total."
    ):
        if no_feedback_count > 0:
            # Get display IDs that need regeneration
            no_feedback_ids = [display_id for display_id, feedback in st.session_state.feedback_data.items() if feedback == 'No']

            st.info(f"Regenerating titles for {len(no_feedback_ids)} display ID(s)...")

            # Create progress bar for regeneration
            progress_bar = st.progress(0)
            status_text = st.empty()

            regenerated_batch = []

            with st.spinner("Regenerating titles..."):
                for i, display_id in enumerate(no_feedback_ids):
                    status_text.text(f"Regenerating ID: {display_id}")
                    progress_bar.progress((i + 1) / len(no_feedback_ids))

                    # Generate new title
                    new_result = title_generator.process_display_id(display_id)

                    if new_result and "error" not in new_result:
                        # Store original title before updating
                        original_title = ""
                        for j, row in enumerate(st.session_state.results_data):
                            if row['display_id'] == display_id:
                                original_title = row['generated_title']
                                # Keep original data but update the generated title and model
                                st.session_state.results_data[j]['generated_title'] = new_result['generated_title']
                                st.session_state.results_data[j]['llm_model_used'] = new_result['llm_model_used']
                                break

                        # Also add to regenerated results for tracking
                        new_result['regeneration_round'] = st.session_state.regenerate_count + 1
                        new_result['original_title'] = original_title
                        regenerated_batch.append(new_result)

            # Clear progress indicators
            progress_bar.empty()
            status_text.empty()

            # Add to regenerated results for history tracking
            st.session_state.regenerated_results.extend(regenerated_batch)

            # Increment regenerate count
            st.session_state.regenerate_count += 1

            # Reset feedback for regenerated items
            for display_id in no_feedback_ids:
                st.session_state.feedback_data[display_id] = ""

            st.success(f"✅ Regenerated {len(regenerated_batch)} titles! (Regeneration {st.session_state.regenerate_count}/3)")
            st.rerun()

    # Show regeneration limit message
    if st.session_state.regenerate_count >= 3:
        st.error("❌ Maximum regeneration limit (3) reached. No more regenerations allowed.")
    elif no_feedback_count == 0:
        st.info("ℹ️ No items marked as 'No' for regeneration.")

# Display regenerated results if available
if st.session_state.regenerated_results:
    st.subheader("🔄 Regenerated Results")
    st.markdown(f"**Total Regenerated Titles:** {len(st.session_state.regenerated_results)} (from {st.session_state.regenerate_count} regeneration rounds)")

    # Group regenerated results by regeneration round
    regenerated_df = pd.DataFrame(st.session_state.regenerated_results)

    for round_num in sorted(regenerated_df['regeneration_round'].unique()):
        round_data = regenerated_df[regenerated_df['regeneration_round'] == round_num]

        with st.expander(f"Regeneration Round {round_num} ({len(round_data)} titles)", expanded=True):
            st.markdown("**Note:** These titles have been updated in the original results table above.")
            st.dataframe(
                round_data,
                use_container_width=True,
                column_config={
                    "display_id": st.column_config.TextColumn("Display ID", width="small"),
                    "original_title": st.column_config.TextColumn("Original Title", width="large"),
                    "generated_title": st.column_config.TextColumn("New Title", width="large"),
                    "url": st.column_config.LinkColumn("Product URL"),
                    "llm_model_used": st.column_config.TextColumn("AI Model", width="small"),
                    "regeneration_round": st.column_config.NumberColumn("Round", width="small"),
                },
                hide_index=True
            )

# Download section (only show if there are results)
if st.session_state.results_data:
    st.subheader("📥 Download Results")

    # Prepare data for download
    original_df = pd.DataFrame(st.session_state.results_data)
    original_df['feedback'] = original_df['display_id'].map(st.session_state.feedback_data).fillna("")

    # Create Excel file with multiple sheets
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        # Original results sheet
        original_df.to_excel(writer, index=False, sheet_name='Original_Results')

        # Regenerated results sheet (if any)
        if st.session_state.regenerated_results:
            regenerated_df = pd.DataFrame(st.session_state.regenerated_results)
            regenerated_df.to_excel(writer, index=False, sheet_name='Regenerated_Results')

        # Summary sheet
        summary_data = {
            'Metric': ['Total Original Results', 'Approved (Yes)', 'Need Regeneration (No)', 'Total Regenerations', 'Regenerated Titles'],
            'Count': [
                len(st.session_state.results_data),
                yes_feedback_count,
                no_feedback_count,
                st.session_state.regenerate_count,
                len(st.session_state.regenerated_results)
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, index=False, sheet_name='Summary')

    output.seek(0)

    st.download_button(
        "📥 Download Complete Results as Excel",
        data=output,
        file_name="product_titles_complete_results.xlsx",
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        help="Downloads Excel file with original results, regenerated results, and summary"
    )

# Sidebar with information
with st.sidebar:
    st.header("ℹ️ How to Use")
    st.markdown("""
    1. **Enter Display IDs**: Add up to 10 product display IDs
    2. **Click Generate**: The AI will create optimized titles
    3. **Review & Audit**: Use feedback dropdown to mark titles as 'Yes' or 'No'
    4. **Regenerate**: Click regenerate button for 'No' cases (max 3 times)
    5. **Download**: Export final results to Excel

    **Supported Formats:**
    - Comma separated: `123, 456, 789`
    - Line separated:
    ```
    123
    456
    789
    ```
    """)

    st.header("🔧 Features")
    st.markdown("""
    - **AI-Powered**: Uses advanced language models
    - **SEO Optimized**: Titles designed for search visibility
    - **Multiple Models**: Randomly selects from different AI approaches
    - **Review Process**: Two-step generation and review
    - **Auditor Feedback**: Editable feedback column with Yes/No options
    - **Smart Regeneration**: Only regenerates 'No' cases, max 3 times
    - **Export Ready**: Download results with feedback as Excel
    """)

    st.header("👥 User Roles")
    st.markdown("""
    **For Users:**
    - Enter display IDs and generate titles
    - Monitor regeneration count (3 max)

    **For Auditors:**
    - Review generated titles
    - Mark as 'Yes' (approved) or 'No' (needs work)
    - Feedback drives regeneration process
    """)

    st.header("📞 Support")
    st.markdown("Need help? Check that your Display IDs are valid IndiaMART product IDs.")

