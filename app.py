import streamlit as st
import pandas as pd
import title_generator  # ✅ Calls your backend
import io

# Page configuration
st.set_page_config(
    page_title="AI Product Title Generator",
    page_icon="🛒",
    layout="wide"
)

# Initialize session state variables
if 'results_data' not in st.session_state:
    st.session_state.results_data = []
if 'regenerate_count' not in st.session_state:
    st.session_state.regenerate_count = 0
if 'original_ids' not in st.session_state:
    st.session_state.original_ids = []

st.title("🛒 AI Product Title Generator")
st.markdown("Generate SEO-optimized product titles using AI. Enter up to 10 Display IDs to get started.")

# Input section
st.subheader("📝 Input Display IDs")
input_ids = st.text_area(
    "Enter Display IDs (comma or newline separated):",
    placeholder="23410447562, 20602194862, 21952292462\nor\n23410447562\n20602194862\n21952292462",
    height=100
)

# Process input
if st.button("🚀 Generate Titles", type="primary"):
    # Parse input - handle both comma and newline separation
    ids = []
    if input_ids:
        # Split by both comma and newline, then clean up
        raw_ids = input_ids.replace(',', '\n').split('\n')
        ids = [i.strip() for i in raw_ids if i.strip()][:10]

    if not ids:
        st.warning("⚠️ Please enter at least one valid display ID.")
    else:
        # Reset session state for new generation
        st.session_state.results_data = []
        st.session_state.regenerate_count = 0
        st.session_state.original_ids = ids.copy()

        st.info(f"Processing {len(ids)} display ID(s)...")

        results = []
        failed_ids = []

        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()

        with st.spinner("Generating titles..."):
            for i, display_id in enumerate(ids):
                status_text.text(f"Processing ID: {display_id}")
                progress_bar.progress((i + 1) / len(ids))

                output_row = title_generator.process_display_id(display_id)
                if output_row and "error" not in output_row:
                    # Set default feedback to empty for new results
                    output_row['feedback'] = ""
                    results.append(output_row)
                else:
                    failed_ids.append(display_id)

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        # Store results in session state
        st.session_state.results_data = results

        # Show failed IDs if any
        if failed_ids:
            st.warning(f"⚠️ Failed to process {len(failed_ids)} ID(s): {', '.join(failed_ids)}")

        if not results:
            st.error("❌ No titles were generated. Please check your Display IDs and try again.")

# Display results if available in session state
if st.session_state.results_data:
    st.success(f"✅ Successfully generated {len(st.session_state.results_data)} titles!")

    # Create DataFrame from session state
    df = pd.DataFrame(st.session_state.results_data)

    # Display results with editable feedback
    st.subheader("📊 Generated Results")
    st.markdown("**Instructions for Auditor:** Please review each generated title and select 'Yes' if approved or 'No' if regeneration is needed.")

    # Create editable data editor
    edited_df = st.data_editor(
        df,
        use_container_width=True,
        column_config={
            "display_id": st.column_config.TextColumn("Display ID", disabled=True),
            "product_name": st.column_config.TextColumn("Product Name", disabled=True),
            "generated_title": st.column_config.TextColumn("Generated Title", width="large", disabled=True),
            "specs": st.column_config.TextColumn("Specifications", width="medium", disabled=True),
            "category": st.column_config.TextColumn("Category", disabled=True),
            "material": st.column_config.TextColumn("Material", width="small", disabled=True),
            "tags": st.column_config.TextColumn("Tags", width="medium", disabled=True),
            "url": st.column_config.LinkColumn("Product URL", disabled=True),
            "feedback": st.column_config.SelectboxColumn(
                "Feedback",
                help="Select 'Yes' if title is approved, 'No' if regeneration needed",
                options=["", "Yes", "No"],
                required=False,
                width="small"
            ),
            "llm_model_used": st.column_config.TextColumn("AI Model", width="small", disabled=True),
        },
        hide_index=True,
        key="results_editor"
    )

    # Update session state with edited data
    st.session_state.results_data = edited_df.to_dict('records')

    # Regenerate functionality
    st.subheader("🔄 Regenerate Titles")

    # Count feedback responses
    no_feedback_count = len([row for row in edited_df.to_dict('records') if row.get('feedback') == 'No'])
    yes_feedback_count = len([row for row in edited_df.to_dict('records') if row.get('feedback') == 'Yes'])

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Approved (Yes)", yes_feedback_count)
    with col2:
        st.metric("Need Regeneration (No)", no_feedback_count)
    with col3:
        st.metric("Regenerations Used", f"{st.session_state.regenerate_count}/3")

    # Regenerate button
    regenerate_disabled = (st.session_state.regenerate_count >= 3) or (no_feedback_count == 0)

    if st.button(
        f"🔄 Regenerate Titles for 'No' Cases ({no_feedback_count} items)",
        type="secondary",
        disabled=regenerate_disabled,
        help="Regenerate titles only for items marked as 'No'. Limited to 3 regenerations total."
    ):
        if no_feedback_count > 0:
            # Get display IDs that need regeneration
            no_feedback_ids = [row['display_id'] for row in edited_df.to_dict('records') if row.get('feedback') == 'No']

            st.info(f"Regenerating titles for {len(no_feedback_ids)} display ID(s)...")

            # Create progress bar for regeneration
            progress_bar = st.progress(0)
            status_text = st.empty()

            with st.spinner("Regenerating titles..."):
                for i, display_id in enumerate(no_feedback_ids):
                    status_text.text(f"Regenerating ID: {display_id}")
                    progress_bar.progress((i + 1) / len(no_feedback_ids))

                    # Generate new title
                    new_result = title_generator.process_display_id(display_id)

                    if new_result and "error" not in new_result:
                        # Find and update the corresponding row in session state
                        for j, row in enumerate(st.session_state.results_data):
                            if row['display_id'] == display_id:
                                # Keep the existing feedback but update other fields
                                new_result['feedback'] = row['feedback']
                                st.session_state.results_data[j] = new_result
                                break

            # Clear progress indicators
            progress_bar.empty()
            status_text.empty()

            # Increment regenerate count
            st.session_state.regenerate_count += 1

            st.success(f"✅ Regenerated {len(no_feedback_ids)} titles! (Regeneration {st.session_state.regenerate_count}/3)")
            st.rerun()

    # Show regeneration limit message
    if st.session_state.regenerate_count >= 3:
        st.error("❌ Maximum regeneration limit (3) reached. No more regenerations allowed.")
    elif no_feedback_count == 0:
        st.info("ℹ️ No items marked as 'No' for regeneration.")

    # Download button
    st.subheader("📥 Download Results")
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        pd.DataFrame(st.session_state.results_data).to_excel(writer, index=False, sheet_name='Generated_Titles')
    output.seek(0)

    st.download_button(
        "📥 Download Results as Excel",
        data=output,
        file_name="generated_titles_with_feedback.xlsx",
        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )

# Sidebar with information
with st.sidebar:
    st.header("ℹ️ How to Use")
    st.markdown("""
    1. **Enter Display IDs**: Add up to 10 product display IDs
    2. **Click Generate**: The AI will create optimized titles
    3. **Review & Audit**: Use feedback dropdown to mark titles as 'Yes' or 'No'
    4. **Regenerate**: Click regenerate button for 'No' cases (max 3 times)
    5. **Download**: Export final results to Excel

    **Supported Formats:**
    - Comma separated: `123, 456, 789`
    - Line separated:
    ```
    123
    456
    789
    ```
    """)

    st.header("🔧 Features")
    st.markdown("""
    - **AI-Powered**: Uses advanced language models
    - **SEO Optimized**: Titles designed for search visibility
    - **Multiple Models**: Randomly selects from different AI approaches
    - **Review Process**: Two-step generation and review
    - **Auditor Feedback**: Editable feedback column with Yes/No options
    - **Smart Regeneration**: Only regenerates 'No' cases, max 3 times
    - **Export Ready**: Download results with feedback as Excel
    """)

    st.header("👥 User Roles")
    st.markdown("""
    **For Users:**
    - Enter display IDs and generate titles
    - Monitor regeneration count (3 max)

    **For Auditors:**
    - Review generated titles
    - Mark as 'Yes' (approved) or 'No' (needs work)
    - Feedback drives regeneration process
    """)

    st.header("📞 Support")
    st.markdown("Need help? Check that your Display IDs are valid IndiaMART product IDs.")

