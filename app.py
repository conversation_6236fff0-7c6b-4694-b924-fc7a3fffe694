import streamlit as st
import pandas as pd
import title_generator  # ✅ Calls your backend
import io

# Page configuration
st.set_page_config(
    page_title="AI Product Title Generator",
    page_icon="🛒",
    layout="wide"
)

st.title("🛒 AI Product Title Generator")
st.markdown("Generate SEO-optimized product titles using AI. Enter up to 10 Display IDs to get started.")

# Input section
st.subheader("📝 Input Display IDs")
input_ids = st.text_area(
    "Enter Display IDs (comma or newline separated):",
    placeholder="23410447562, 20602194862, 21952292462\nor\n23410447562\n20602194862\n21952292462",
    height=100
)

# Process input
if st.button("🚀 Generate Titles", type="primary"):
    # Parse input - handle both comma and newline separation
    ids = []
    if input_ids:
        # Split by both comma and newline, then clean up
        raw_ids = input_ids.replace(',', '\n').split('\n')
        ids = [i.strip() for i in raw_ids if i.strip()][:10]

    if not ids:
        st.warning("⚠️ Please enter at least one valid display ID.")
    else:
        st.info(f"Processing {len(ids)} display ID(s)...")

        results = []
        failed_ids = []

        # Create progress bar
        progress_bar = st.progress(0)
        status_text = st.empty()

        with st.spinner("Generating titles..."):
            for i, display_id in enumerate(ids):
                status_text.text(f"Processing ID: {display_id}")
                progress_bar.progress((i + 1) / len(ids))

                output_row = title_generator.process_display_id(display_id)
                if output_row and "error" not in output_row:
                    results.append(output_row)
                else:
                    failed_ids.append(display_id)

        # Clear progress indicators
        progress_bar.empty()
        status_text.empty()

        # Display results
        if results:
            st.success(f"✅ Successfully generated {len(results)} titles!")

            # Create DataFrame and display
            df = pd.DataFrame(results)

            # Display results in a nice format
            st.subheader("📊 Generated Results")
            st.dataframe(
                df,
                use_container_width=True,
                column_config={
                    "url": st.column_config.LinkColumn("Product URL"),
                    "generated_title": st.column_config.TextColumn("Generated Title", width="large"),
                    "specs": st.column_config.TextColumn("Specifications", width="medium"),
                    "material": st.column_config.TextColumn("Material", width="small"),
                    "tags": st.column_config.TextColumn("Tags", width="medium"),
                    "llm_model_used": st.column_config.TextColumn("AI Model", width="small"),
                }
            )

            # Download button
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Generated_Titles')
            output.seek(0)

            st.download_button(
                "📥 Download Results as Excel",
                data=output,
                file_name="generated_titles.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )

        # Show failed IDs if any
        if failed_ids:
            st.warning(f"⚠️ Failed to process {len(failed_ids)} ID(s): {', '.join(failed_ids)}")

        if not results:
            st.error("❌ No titles were generated. Please check your Display IDs and try again.")

# Sidebar with information
with st.sidebar:
    st.header("ℹ️ How to Use")
    st.markdown("""
    1. **Enter Display IDs**: Add up to 10 product display IDs
    2. **Click Generate**: The AI will create optimized titles
    3. **Review Results**: Check the generated titles in the table
    4. **Download**: Export results to Excel for further use

    **Supported Formats:**
    - Comma separated: `123, 456, 789`
    - Line separated:
    ```
    123
    456
    789
    ```
    """)

    st.header("🔧 Features")
    st.markdown("""
    - **AI-Powered**: Uses advanced language models
    - **SEO Optimized**: Titles designed for search visibility
    - **Multiple Models**: Randomly selects from different AI approaches
    - **Review Process**: Two-step generation and review
    - **Export Ready**: Download results as Excel
    """)

    st.header("📞 Support")
    st.markdown("Need help? Check that your Display IDs are valid IndiaMART product IDs.")

