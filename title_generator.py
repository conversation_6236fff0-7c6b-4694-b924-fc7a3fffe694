import requests
import re
import random

# CONFIG
GEMINI_API_KEY = 'AIzaSyCAS2PIT1DeQHe8PssfO8gj2K9XC65Uag0'  # Replace with your actual API key
API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent'

# PDP API fetcher
def fetch_pdp_data(display_id):
    url = f"https://www.indiamart.com/api/ajax-services/sample_pdp_response/?displayid={display_id}"
    try:
        resp = requests.get(url, timeout=10)
        resp.raise_for_status()  # Ensure we catch any HTTP errors
        data = resp.json()
        if data.get("Status") == 200 and data.get("Data"):
            item = data["Data"][0]
            category = item.get("BRD_MCAT_NAME", "")
            material = ""   #check: why this?
            tags = {}
            product_name = item.get("PC_ITEM_DISPLAY_NAME") or item.get("PC_ITEM_NAME", "")
            url = f"https://www.indiamart.com/proddetail/{item.get('PC_ITEM_DISPLAY_ID', display_id)}.html"

            for spec in item.get("ISQ", []):
                name = spec.get("FK_IM_SPEC_MASTER_DESC", "").lower()
                val = spec.get("SUPPLIER_RESPONSE_DETAIL", "")
                if "material" in name:
                    material = val
                elif val:
                    tags[name] = val  # store name-value pair

            tags_str = ", ".join(f"{k}: {v}" for k, v in tags.items())

            #check: what others can i send to the prompt?
            return {
                "display_id": display_id,
                "product_name": product_name,
                "specs": tags_str,
                "category": category,
                "material": material,
                "tags": tags_str,
                "url": url
            }
    except requests.exceptions.RequestException as e:
        print(f"❌ PDP fetch failed for {display_id}: {e}")
    except Exception as e:
        print(f"❌ Unexpected error fetching PDP data for {display_id}: {e}")
    return None

# Dynamic prompt functions for different LLM models
def build_prompt_chatgpt(product):
    #check: max ~12 words
    return f"""
You are a professional e-commerce copywriter and SEO specialist.

Your goal is to generate a grammatically correct, human-like, search-optimized product title based on the structured product data provided below.

🎯 Guidelines:
- Title must be **accurate**, **concise**, and **contextually relevant**
- Avoid redundancy, keyword stuffing, or robotic phrasing
- Do **not** hallucinate attributes that are not provided
- Respect category context, but do not hardcode templates
- Accept natural variations (e.g., "Black Wallet for Men" and "Men's Black Wallet" are both valid)

🧾 Product Information:
- Product Name: {product['product_name']}
- ISQ Details: {product['specs']}
- Subcategory: {product['category']}

🔄 Objective:
Write **1 clean, SEO-optimized product title** (max ~12 words) that:
- Uses proper word order and grammar
- Prioritizes search relevance
- Is not templated or repetitive
- Introduces subtle linguistic variation across similar inputs

Output only the title. Do not include explanations or extra lines.
"""

def build_prompt_bard(product_data: dict) -> str:
    """
    Generates a comprehensive prompt for an AI model (like ChatGPT) to create an
    SEO-optimized, grammatically correct, and professionally formatted product title.

    The generated prompt will guide the AI to act as an expert product title generator,
    focusing on accuracy, conciseness, non-redundancy, search optimization, and
    natural linguistic variation, avoiding robotic phrasing or hardcoded rules.

    Args:
        product_data (dict): A dictionary containing product information,
                             expected to have keys like 'Product Name',
                             'Product Specifications', 'Item ID', 'Product URL'.

    Returns:
        str: A detailed prompt for the AI to generate a product title.
    """

    product_name = product_data.get('product_name', 'Unknown Product')
    product_specs = product_data.get('specs', 'No specific details available.')
    Subcategory = product_data.get('category', 'N/A')

    prompt = f"""
You are an expert E-commerce Product Title Generator, specializing in creating SEO-optimized, grammatically correct, and professionally formatted product display names. Your goal is to transform raw product data into compelling and search-friendly titles that avoid redundancy, poor grammar, and awkward phrasing.

- **Existing Product Name:** "{product_name}"
- **Product Specifications:** "{product_specs}"
- **Subcategory:** "{Subcategory}"

**Task:**
Generate a single, optimal **Product Display Name** based *only* on the provided product data. The title should be:
1.  **Accurate & Concise:** Reflect all crucial information without being overly long.
2.  **Contextually Appropriate:** Understand the implied product category and relevance of specifications.
3.  **Non-Redundant:** Eliminate repetitive keywords (e.g., "Men Male Wallet" should be "Men's Wallet").
4.  **Search-Optimized:** Prioritize keywords that customers are likely to use in searches.
5.  **Grammatically Correct & Professionally Formatted:** Ensure proper sentence structure, capitalization, and punctuation.
6.  **Naturally Varied:** Introduce linguistic variations where appropriate (e.g., "Men's Black Leather Wallet" vs. "Black Leather Wallet for Men") to avoid robotic phrasing.

**Critical Constraints (Adhere Strictly):**
-   **DO NOT hallucinate attributes or assume specifications** unless explicitly mentioned or clearly implied by the provided data.
-   **DO NOT hardcode static rules.** Leverage your generative intelligence to dynamically understand and prioritize keywords.
-   **The output must be ONLY the generated product title.** Do not include any introductory or concluding remarks, explanations, or additional text.

**Examples of Transformations:**
-   **Input Issue:** "For lining, bag cotton Bag Fabric"
    **Desired Output Style:** "Cotton Fabric for Bag Lining"
-   **Input Issue:** "Female Black Ladies Plain Leather Wallet"
    **Desired Output Style:** "Black Plain Leather Wallet for Women"
-   **Input Issue:** "Cotton cream cool cottan Carry bag Fabrics, GSM: 250-300"
    **Desired Output Style:** "Cream Cotton Carry Bag Fabric, GSM: 250–300"

Generate the best possible Product Display Name now.
"""
    return prompt

def build_prompt_deepseek(product_data: dict) -> str:
    return f"""
### Role ###
You are an expert E-commerce Product Title Generator with 10 years of experience in SEO 
and conversion optimization. Your task is to transform raw product data into **human-like, 
search-friendly titles** that outperform rule-based systems.

### Input Data ###
- Product Name: {product_data.get('product_name', 'N/A')}
- Specifications: {product_data.get('specs', 'N/A')}
- Category: {product_data.get('category', 'N/A')}

### Title Requirements ###
1. **Accuracy**:
   - Use ONLY provided attributes. Never assume unstated specs (e.g., don't add "for Men" 
     if only "Male" is mentioned).
   
2. **SEO Optimization**:
   - Front-load important keywords (e.g., "Leather Wallet for Men" → "Men's Leather Wallet").
   - Include critical specs (e.g., "GSM 250-300" for fabrics).

3. **Grammar & Formatting**:
   - Fix errors (e.g., "cottan" → "Cotton").
   - Use title case (e.g., "Black Leather Wallet").

4. **Redundancy Removal**:
   - Merge duplicates (e.g., "Female Ladies Wallet" → "Women's Wallet").

5. **Natural Variation**:
   - Allow flexible phrasing (e.g., both "Men's Black Wallet" and "Black Wallet for Men" are valid).

### Critical Constraints ###
- ❌ NO HALLUCINATION: Never invent unstated attributes.
- ❌ NO TEMPLATES: Avoid rigid structures like "[Material][Product][Audience]".
- ❌ NO EXPLANATIONS: Output ONLY the title (max 12 words).

### Bad vs Good Examples ###
1. ❌ "Men Male Leather Wallet" → ✅ "Men's Genuine Leather Wallet"
2. ❌ "bag cotton Bag Fabric" → ✅ "Cotton Bag Lining Fabric"
3. ❌ "cool cottan Carry bag" → ✅ "Cool Cotton Carry Bag, GSM 250-300"

### Task ###
Generate ONE perfect title based on the above rules. Output ONLY the title:
"""

# Function to select a random LLM model
def get_random_prompt_function():
    prompt_functions = [build_prompt_chatgpt, build_prompt_bard, build_prompt_deepseek]
    return random.choice(prompt_functions)

# Chained generation with review
def generate_title_with_review(prompt, display_id):
    headers = {
        "Content-Type": "application/json",
        "X-goog-api-key": GEMINI_API_KEY
    }

    # Step 1: Generate initial title
    try:
        res1 = requests.post(API_URL, headers=headers, json={"contents": [{"parts": [{"text": prompt}]}]})
        res1.raise_for_status()  # Catch any HTTP errors in the first request
        first_title = res1.json()['candidates'][0]['content']['parts'][0]['text'].strip()
        print(f"\n🔹 First Title ({display_id}): {first_title}")
    except requests.exceptions.RequestException as e:
        print(f"❌ First gen error for {display_id}: {e}")
        return "GENERATION_FAILED"
    except Exception as e:
        print(f"❌ Unexpected error in first gen for {display_id}: {e}")
        return "GENERATION_FAILED"

    # Step 2: Review and improve
    review_prompt = f"""
Original Prompt:
{prompt}

Generated Title:
{first_title}

Instructions:
1. Evaluate if this title is suitable for the input.
2. If it's good, reply with: ✅ Title is good: <title>
3. If not, reply with: ❌ Improved Title: <better version>
"""

    try:
        res2 = requests.post(API_URL, headers=headers, json={"contents": [{"parts": [{"text": review_prompt}]}]})
        res2.raise_for_status()  # Catch any HTTP errors in the second request
        review_text = res2.json()['candidates'][0]['content']['parts'][0]['text'].strip()
        print(f"🔁 Reviewed Title ({display_id}): {review_text}")

        match = re.search(r"(?:✅|❌)?\s*(?:Title\s*(?:is\s*good|Improved)?[:\-]?)\s*(.+)", review_text, flags=re.IGNORECASE)
        return match.group(1).strip() if match else first_title
    except requests.exceptions.RequestException as e:
        print(f"❌ Review step error for {display_id}: {e}")
        return first_title
    except Exception as e:
        print(f"❌ Unexpected error in review step for {display_id}: {e}")
        return first_title



def process_display_id(display_id: str):
    product = fetch_pdp_data(display_id)
    if not product:
        return {"display_id": display_id, "error": "PDP data not found"}

    prompt_func = get_random_prompt_function()
    prompt = prompt_func(product)
    final_title = generate_title_with_review(prompt, display_id)

    return {
        "display_id": product['display_id'],
        "product_name": product['product_name'],
        "specs": product['specs'],
        "category": product['category'],
        "material": product['material'],
        "tags": product['tags'],
        "url": product['url'],
        "generated_title": final_title,
        "feedback": "",
        "llm_model_used": prompt_func.__name__
    }


